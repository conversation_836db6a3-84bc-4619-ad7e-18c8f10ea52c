import { useRecoilValue } from 'recoil';
import store from '~/store';
import ListEmails from './ListEmails';
import EmailTemplate from './templates/EmailTemplate';

const Mails = () => {
  // const [jobSimulationEmails, setJobSimulationEmails] = useRecoilState(store.jobSimulationEmails);
  const jobSimulationCurrentEmail = useRecoilValue(store.jobSimulationCurrentEmail);
  // const ComponentEmailTemplate = templateEmails[jobSimulationCurrentEmail?.emailComponentName || 'default'];

  return (
    <div className="flex h-full w-full min-h-0 bg-white">
      <div className="flex flex-1 min-h-0">
        {/* List Emails */}
        <aside
          className="w-[320px] border-r border-neutral-200 p-5 h-full overflow-y-auto"
          style={{ height: `${jobSimulationCurrentEmail ? 'calc(100% - 60px)' : '100%'}` }}
        >
          <div className="space-y-2">
            <ListEmails />
          </div>
        </aside>

        {/* Email Content */}
        <main className="flex-1 p-4 h-full min-h-0 overflow-y-auto" style={{ height: `${jobSimulationCurrentEmail ? 'calc(100% - 60px)' : '100%'}` }}>
          {jobSimulationCurrentEmail && (
            <EmailTemplate
              title={jobSimulationCurrentEmail.title}
              senderName={jobSimulationCurrentEmail.name}
              senderEmail={jobSimulationCurrentEmail.email}
              senderAvatar={jobSimulationCurrentEmail.avatar || '/assets/julie.jpeg'}
              senderRole={jobSimulationCurrentEmail.role}
              time={jobSimulationCurrentEmail.time!}
              data={jobSimulationCurrentEmail.data}
            >
              {/* {ComponentEmailTemplate ? (
                  <ComponentEmailTemplate email={jobSimulationCurrentEmail} />
                ) : (
                  <></>
                )} */}
            </EmailTemplate>
          )}
          {/* <div
              className="rounded-xl border p-5"
            >
            <div className="flex items-center justify-between">
              <p className="text-2xl font-bold">Welcome Aboard Meeting!</p>
              <span className="cursor-pointer rounded-xl bg-[#F0F0F0] p-1.5">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="4"
                  height="20"
                  viewBox="0 0 4 20"
                  fill="none"
                >
                  <path
                    d="M-8.74228e-08 2C-1.35705e-07 3.10457 0.89543 4 2 4C3.10457 4 4 3.10457 4 2C4 0.89543 3.10457 -3.91405e-08 2 -8.74228e-08C0.895431 -1.35705e-07 -3.91405e-08 0.89543 -8.74228e-08 2Z"
                    fill="#111111"
                  />
                  <path
                    d="M-8.74228e-08 10C-1.35705e-07 11.1046 0.89543 12 2 12C3.10457 12 4 11.1046 4 10C4 8.89543 3.10457 8 2 8C0.895431 8 -3.91405e-08 8.89543 -8.74228e-08 10Z"
                    fill="#111111"
                  />
                  <path
                    d="M-8.74228e-08 18C-1.35705e-07 19.1046 0.89543 20 2 20C3.10457 20 4 19.1046 4 18C4 16.8954 3.10457 16 2 16C0.895431 16 -3.91405e-08 16.8954 -8.74228e-08 18Z"
                    fill="#111111"
                  />
                </svg>
              </span>
            </div>
            <hr className="my-5" />
            <div className="space-y-5">
              <div className="flex items-start gap-4">
                <img
                  src="/assets/avatar.png"
                  alt="Avatar"
                  className="h-12 w-12 rounded-full object-cover"
                />
                <div className="flex-1">
                  <p className="font-semibol">Admin</p>
                  <div className="flex items-center text-xs text-neutral-500">
                    <span className="mr-1"><EMAIL></span>
                    <span className="cursor-pointer">
                      <IconAdmin />
                    </span>
                    <span className="mx-2 h-1 w-1 rounded-full bg-[#000] opacity-50"></span>
                    <span className="text-xs opacity-50">22 hours ago</span>
                  </div>
                </div>
              </div>
              <img src="/assets/greentek-logo.png" alt="Logo" />
              <div className="space-y-4 text-sm">
                <p className="font-semibold">Hi User Name,</p>
                <p>Welcome to your Internship.</p>
                <p className="w-full max-w-3xl">
                  First of all — congratulations and welcome to the team! 🎉
                  <br />
                  We're incredibly excited to have you join us as a Junior Software Developer, and
                  we believe your journey here will be filled with learning, meaningful impact, and
                  awesome collaboration.
                  <br />
                  This is just the beginning of something great. You’re stepping into an environment
                  where ideas are shared freely, support is always around the corner, and growth is
                  part of the process. 🚀
                  <br />
                  To kick things off, we’d love to meet you in person (virtually!) and give you a
                  warm introduction to the team, share more about what’s ahead, and make sure you’re
                  all set for your journey.
                </p>
              </div>
              <div className="flex flex-col">
                <p className="mb-4 text-sm font-semibold">Welcome Meeting Details:</p>
                <div className="inline-grid w-fit grid-cols-[auto_auto] gap-x-8 gap-y-1 text-sm text-neutral-900 dark:text-neutral-100">
                  <p>Date:</p>
                  <p>Tuesday, April 15, 2025</p>

                  <p>Time:</p>
                  <p>10:00 AM (GMT+7)</p>

                  <p>Duration:</p>
                  <p>~30 minutes</p>
                </div>
                <button
                  className="mt-3 w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
                  onClick={() => handleJoinMeeting()}
                >
                  Join the Meeting
                </button>
                <p className="mt-4 flex flex-col gap-2 text-sm">
                  <span>Kind regards,</span>
                  <span className="font-semibold">ABC Team</span>
                </p>
              </div>
            </div>
          </div> */}
        </main>
      </div>
    </div>
  );
};

export default Mails;
