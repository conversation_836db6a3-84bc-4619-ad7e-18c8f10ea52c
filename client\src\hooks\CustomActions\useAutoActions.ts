import { useEffect } from 'react';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { AutoAction } from '~/common';
import store from '~/store';
import useOpenCloseApp from '../useOpenCloseApp';

export default function useAutoActions() {
  const { openApp } = useOpenCloseApp();
  const [autoActions, setAutoActions] = useRecoilState(store.autoActions);
  const setIsOpenWorkPortal = useSetRecoilState(store.jobSimulationIsOpenWorkPortal);

  const setAutoActionWithText = (text: string) => {
    // ::callToAction{identifier="open-email-app" title="Open email app" type="auto-action" command="open-email-app"}
    const blockRegex = /::callToAction\{(?=[^}]*type="auto-action")([^}]+)\}/;
    const match = text.match(blockRegex);
    if (!match) return;
    const attrsStr = match[1]; // Contains title="..." type="..." ...
    const attrRegex = /(\w+)="(.*?)"/g;
    let matchAttr: RegExpExecArray | null;
    const autoAction: AutoAction = { id: '', command: '', isExecuted: false };
    let id = '';
    let type = '';
    while ((matchAttr = attrRegex.exec(attrsStr)) !== null) {
      const [, key, value] = matchAttr;
      if (key === 'identifier') {
        id = value;
        autoAction.id = id;
      } else if (key === 'command') {
        autoAction.command = value;
      } else if (key === 'type') {
        type = value;
      } else if (key === 'delay') {
        let delay = parseInt(value || '0', 10);
        if (isNaN(delay)) delay = 0;
        autoAction.delay = delay;
      }
    }
    if (!id || type !== 'auto-action' || !autoAction.command) return;
    setAutoActions((prev) => ({ ...prev, [id]: autoAction }));
  };

  const runAutoAction = (id: string) => {
    const action = autoActions[id];
    if (!action || action.isExecuted) {
      console.log(`Auto action with ID: ${id} has already been executed or does not exist.`);
      return;
    }

    let executeFunc = () => {};

    if (action.command === 'open-work-portal') {
      executeFunc = () => setIsOpenWorkPortal(true);
    } else if (action.command === 'open-email-app') {
      executeFunc = () => openApp('mail');
    } else if (action.command === 'open-meeting-app') {
      executeFunc = () => openApp('meeting');
    } else if (action.command === 'open-task-board-app') {
      executeFunc = () => openApp('task-board');
    } else if (action.command === 'open-news-app') {
      executeFunc = () => openApp('news');
    }

    setTimeout(
      () => {
        executeFunc();
      },
      (action.delay || 0) * 1000,
    );

    setAutoActions((prev) => ({ ...prev, [id]: { ...action, isExecuted: true } }));
  };

  useEffect(() => {
    const lastAction = Object.values(autoActions).find((action) => !action.isExecuted);

    if (lastAction) {
      runAutoAction(lastAction.id);
    }
  }, [autoActions, runAutoAction]);

  return {
    setAutoActionWithText,
    runAutoAction,
  };
}
