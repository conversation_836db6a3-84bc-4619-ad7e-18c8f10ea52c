import AutoMessageButton from './AutoMessageButton';
import CopyMessageButton from './CopyMessageButton';
import RedirectButton from './RedirectButton';
import WebviewButton from './WebviewButton';

export type CustomButtonProps = {
  command: string;
  type: string;
  title: string;
  text: string;
  link: string;
  webview?: string;
  delay?: string;
};

const CustomButton = ({ type, title, text, webview, link, delay = '0' }: CustomButtonProps) => {
  if (type === 'auto-message') {
    return <AutoMessageButton title={title} text={text} delay={delay ? parseInt(delay) : 0} />;
  }
  if (['webview-action', 'webview-virtual-world-action'].includes(type)) {
    return <WebviewButton type={type} title={title} text={text} webview={webview || ''} />;
  }
  if (type === 'copy') {
    return <CopyMessageButton title={title} text={text} delay={delay ? parseInt(delay) : 0} />;
  }
  if (type === 'redirect') {
    return <RedirectButton title={title} link={link} />;
  }

  return null;
};

export default CustomButton;
