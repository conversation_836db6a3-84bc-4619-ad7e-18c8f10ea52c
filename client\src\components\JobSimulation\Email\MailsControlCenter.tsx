import { useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { TJobSimulationContext, TJobSimulationMeeting } from '~/common';
import {
  useSaveUserInteraction,
  useUpdateJobSimulationProgress,
} from '~/data-provider/JobSimulation/mutations';
import store from '~/store';

const MailsControlCenter = () => {
  const { jobSimulationId, jobSimulationData } = useOutletContext<TJobSimulationContext>();
  const [jobSimulationEmails, setJobSimulationEmails] = useRecoilState(store.jobSimulationEmails);
  const jobSimulationCurrentEmail = useRecoilValue(store.jobSimulationCurrentEmail);
  const jobSimulationUser = useRecoilValue(store.jobSimulationUser);
  const [jobSimulationPushNextEmail, setJobSimulationPushNextEmail] = useRecoilState(
    store.jobSimulationPushNextEmail,
  );
  const setJobSimulationEnabledApps = useSetRecoilState(store.jobSimulationEnabledApps);
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);
  const setJobsimulationMeetings = useSetRecoilState(store.jobSimulationMeetings);
  const setJobsimulationCurrentMeeting = useSetRecoilState(store.jobSimulationCurrentMeeting);
  const lastEmail = jobSimulationEmails[jobSimulationEmails.length - 1];

  const saveUserInteraction = useSaveUserInteraction();
  const updateJobSimulationProgress = useUpdateJobSimulationProgress();

  useEffect(() => {
    if (!!jobSimulationData?.emails?.length && !!jobSimulationData?.progress?.emails?.length) {
      setJobSimulationPushNextEmail(false);
      const ids = (jobSimulationData.progress?.emails || []).map((email) => email.id);
      const initEmails = jobSimulationData.emails.filter((email) => ids.includes(email.id));
      setJobSimulationEmails(
        initEmails.map((email) => ({
          ...email,
          read:
            (jobSimulationData.progress?.emails || []).find((e) => e.id === email.id)?.read ||
            false,
          time:
            (jobSimulationData.progress?.emails || []).find((e) => e.id === email.id)?.time ||
            Date.now(),
        })),
      );
    }
    if (jobSimulationData?.progress?.enabledApps) {
      setJobSimulationEnabledApps([
        ...(jobSimulationData.progress.enabledApps || ['mail', 'news']),
        'facebook-ad',
        'fb-ad',
      ]);
    }
  }, []);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (jobSimulationPushNextEmail && !!jobSimulationData?.emails?.length) {
      const nextEmail = lastEmail
        ? (jobSimulationData?.emails || []).find((email) => email.id === lastEmail.nextEmailId)
        : (jobSimulationData?.emails || [])[0];
      if (!nextEmail || jobSimulationEmails.find((email) => email.id === nextEmail.id)) {
        setJobSimulationPushNextEmail(false);
        return;
      }
      timer = setTimeout(() => {
        setJobSimulationEmails([
          ...jobSimulationEmails,
          { ...nextEmail, read: false, time: Date.now() },
        ]);
        updateJobSimulationProgress.mutateAsync({
          jobSimulationId: jobSimulationId!,
          emails: [...jobSimulationEmails, { id: nextEmail.id, read: false, time: Date.now() }],
        });
        setJobSimulationPushNextEmail(false);
        nextEmail.triggerActions?.forEach((action) => {
          if (action.data?.when === 'receive') {
            if (action.type === 'triggerAssistant') {
              setJobsimulationTriggerMessage({
                message: action.data?.triggerMessage || '',
                isTriggered: true,
              });
            } else if (action.type === 'enableApps' && !!action.data.appIds?.length) {
              setJobSimulationEnabledApps(action.data.appIds || []);
              updateJobSimulationProgress.mutateAsync({
                jobSimulationId: jobSimulationId!,
                enabledApps: action.data.appIds,
              });
            }
          }
        });
      }, 500);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [jobSimulationPushNextEmail, jobSimulationData?.emails?.length]);

  useEffect(() => {
    let timers: NodeJS.Timeout[] = [];
    if (
      jobSimulationCurrentEmail?.triggerActions &&
      !jobSimulationEmails.find((email) => email.id === jobSimulationCurrentEmail.id)?.read
    ) {
      for (const action of jobSimulationCurrentEmail.triggerActions) {
        if (action.data?.when !== 'open') continue;
        if (action.type === 'nextEmail') {
          const timer = setTimeout(
            () => {
              setJobSimulationPushNextEmail(true);
            },
            (action.data?.triggerTimeout || 0) * 1000,
          );
          timers.push(timer);
        } else if (action.type === 'triggerAssistant') {
          const timer = setTimeout(
            () => {
              setJobsimulationTriggerMessage({
                message: action.data?.triggerMessage || '',
                isTriggered: true,
              });
            },
            (action.data?.triggerTimeout || 0) * 1000,
          );
          timers.push(timer);
        } else if (action.type === 'enableApps' && !!action.data.appIds?.length) {
          setJobSimulationEnabledApps(action.data.appIds || []);
          updateJobSimulationProgress.mutateAsync({
            jobSimulationId: jobSimulationId!,
            enabledApps: action.data.appIds,
          });
        }
      }
      // jobSimulationCurrentEmail.triggerActions.forEach((action) => {
      //   if (action.type === 'nextEmail') {
      //     const timer = setTimeout(
      //       () => {
      //         setJobSimulationPushNextEmail(true);
      //       },
      //       (action.data?.triggerTimeout || 0) * 1000,
      //     );
      //     timers.push(timer);
      //   } else if (action.type === 'triggerAssistant') {
      //     const timer = setTimeout(
      //       () => {
      //         setJobsimulationTriggerMessage({
      //           message: action.data?.triggerMessage || '',
      //           isTriggered: true,
      //         });
      //       },
      //       (action.data?.triggerTimeout || 0) * 1000,
      //     );
      //     timers.push(timer);
      //   }
      // });
    }
    return () => {
      timers.forEach((timer) => {
        if (timer) clearTimeout(timer);
      });
    };
  }, [jobSimulationCurrentEmail]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('Received message ::: ', event.data, lastEmail);
      const { type, data } = event.data || {};
      if (type === 'INTRODUCTION_STATUS' && data.progress === 'End') {
        setJobsimulationMeetings((prevMeetings: TJobSimulationMeeting[]) => {
          const newMeetings = prevMeetings.map((m) => {
            return m.id === data.meetingId
              ? ({ ...m, status: 'completed' } as TJobSimulationMeeting)
              : m;
          });
          return newMeetings;
        });
        setJobsimulationCurrentMeeting(null);
        saveUserInteraction.mutate({
          jobSimulationId: jobSimulationId!,
          jobSimulationEmail: jobSimulationUser?.email!,
          interaction: {
            type: 'complete-meeting',
            appData: {
              meetingId: data.meetingId,
            },
          },
        });
        const meetingAction = lastEmail?.data?.actions?.find(
          (action) => action.type === 'joinMeeting',
        );
        if (meetingAction) {
          if (meetingAction.data?.triggerAssistant)
            setJobsimulationTriggerMessage({
              message: meetingAction.data?.triggerAssistant,
              isTriggered: true,
            });
          if (meetingAction.data?.pushNextEmail) setJobSimulationPushNextEmail(true);
          if (meetingAction.data?.enableApps?.length) {
            setJobSimulationEnabledApps(meetingAction.data.enableApps);
            updateJobSimulationProgress.mutateAsync({
              jobSimulationId: jobSimulationId!,
              enabledApps: meetingAction.data?.enableApps,
            });
          }
        }

        return;
      }
      if (type === 'BILLION_TASK' && data) {
        const eventBillionTaskType = data?.type;
        if (!eventBillionTaskType) return;
        if (eventBillionTaskType === 'SUBMIT') {
          if (data?.status === 'passed') {
            const isLastTask = !!data?.isLastTask;
            setJobsimulationTriggerMessage({
              message: isLastTask
                ? "I've completed all tasks"
                : `I've completed the task ${data.taskName || ''}`,
              isTriggered: true,
            });
            if (isLastTask) {
              updateJobSimulationProgress.mutateAsync({
                jobSimulationId: jobSimulationId!,
                status: 'completed',
              });
              // TODO: refactor: push the reference letter email to user
              setJobSimulationPushNextEmail(true);
            }

            saveUserInteraction.mutate({
              jobSimulationId: jobSimulationId!,
              jobSimulationEmail: jobSimulationUser?.email!,
              interaction: {
                type: 'complete-task',
                appData: {
                  taskId: data.taskId,
                },
              },
            });
          }

          return;
        }

        if (eventBillionTaskType === 'CONGRATS') {
          setJobsimulationTriggerMessage({
            message: `I've completed all tasks`,
            isTriggered: true,
          });
          updateJobSimulationProgress.mutateAsync({
            jobSimulationId: jobSimulationId!,
            status: 'completed',
          });
          // TODO: refactor: push the reference letter email to user
          setJobSimulationPushNextEmail(true);

          return;
        }

        if (eventBillionTaskType === 'TASKS_INFO') {
          const tasks = data?.taskDetails || [];

          saveUserInteraction.mutate({
            jobSimulationId: jobSimulationId!,
            jobSimulationEmail: jobSimulationUser?.email!,
            interaction: {
              type: 'open-app',
              appData: {
                appId: 'task-board',
                tasks,
              },
            },
          });

          return;
        }

        if (eventBillionTaskType === 'TASK_INFO') {
          const taskInfo = {
            taskId: data?.taskId,
            taskName: data?.taskName,
            taskDescription: data?.taskDescription,
            taskStatus: data?.taskStatus,
          };

          saveUserInteraction.mutate({
            jobSimulationId: jobSimulationId!,
            jobSimulationEmail: jobSimulationUser?.email!,
            interaction: {
              type: 'open-app',
              appData: {
                appId: 'task-board',
                task: taskInfo,
              },
            },
          });

          return;
        }

        return;
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [lastEmail]);

  return <></>;
};

export default MailsControlCenter;
