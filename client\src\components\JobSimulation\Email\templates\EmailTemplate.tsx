import { XMarkIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import store from '~/store';
import { cn } from '~/utils';
import EmailClock from '../EmailClock';
import MarkdownEmail from './MarkdownEmail';

const ActionButton = ({
  action,
  label,
  title,
}: {
  action: { (): void };
  label: string;
  title?: string;
}) => {
  return (
    <>
      {title && <p className="text-sm text-black dark:text-neutral-100">{title}</p>}
      <button
        className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
        onClick={action}
      >
        {label}
      </button>
    </>
  );
};

// Handle actions
const useEmailActions = (email: EmailTemplateProps, actionDataIndex: number) => {
  const setJobSimulationApp = useSetRecoilState(store.jobSimulationApp);
  const setJobSimulationPushNextEmail = useSetRecoilState(store.jobSimulationPushNextEmail);
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);
  const setJobSimulationEmailFile = useSetRecoilState(store.jobSimulationEmailFile);
  const jobSimulationUser = useRecoilValue(store.jobSimulationUser);

  const objAction = email.data?.actions?.[actionDataIndex] || null;
  if (!objAction) return null;

  const { type: actionType, data: actionData } = objAction;

  let component = <></>;
  let action: { (): void } = () => {};

  switch (actionType) {
    case 'joinMeeting':
      let meetingDateTime: Date | number | string | undefined = actionData?.datetime || email.time;
      if (typeof meetingDateTime === 'string' && meetingDateTime.includes('email_time')) {
        meetingDateTime = email.time;
      }
      action = () => {
        setJobSimulationApp({
          appId: 'meeting',
          // data: { link: actionData?.meetingLink || '' },
        });
        // setJobSimulationMeeting({
        //   title: objAction.title || '',
        //   datetime: Number(meetingDateTime),
        //   link: actionData?.meetingLink || '',
        // });
        // if (actionData?.pushNextEmail) {
        //   setJobSimulationPushNextEmail(true);
        // }
        // if (actionData?.triggerAssistant) {
        //   // TODO: only trigger one time
        //   setJobsimulationTriggerMessage({
        //     message: actionData?.triggerAssistant || '',
        //     isTriggered: true,
        //   });
        // }
      };

      meetingDateTime = new Date(meetingDateTime);
      component = (
        <>
          <div className="inline-grid w-fit grid-cols-[auto_auto] gap-x-8 gap-y-1 text-sm text-black">
            <p>Date:</p>
            {/* <p>Tuesday, April 15, 2025</p> */}
            <p>{format(meetingDateTime, 'EEEE, MMMM d, yyyy')}</p>

            <p>Time:</p>
            {/* <p>10:00 AM (GMT+7)</p> */}
            <p>{format(meetingDateTime, 'hh:mm aa (OOO)')}</p>

            {actionData?.duration && (
              <>
                <p>Duration:</p>
                <p>{actionData.duration}</p>
              </>
            )}
          </div>

          <ActionButton action={action} label={objAction.label || 'Join the Meeting'} />
        </>
      );
      break;
    case 'viewTasks':
      action = () => {
        setJobSimulationApp({
          appId: 'task-board',
        });
        setJobSimulationPushNextEmail(true);
      };
      component = (
        <>
          <ActionButton
            action={action}
            title={objAction.title}
            label={objAction.label || 'Tasks'}
          />
        </>
      );
      break;
    case 'viewNewsDetails':
      action = () => {
        setJobSimulationApp({
          appId: 'news',
          data: { newsId: actionData?.newsId },
        });
        setJobSimulationPushNextEmail(true);
      };
      component = (
        <>
          <ActionButton
            action={action}
            title={objAction.title}
            label={objAction.label || 'News details'}
          />
        </>
      );
      break;
    case 'viewTaskDetails':
      action = () => {
        setJobSimulationApp({
          appId: 'task-board',
          data: { taskId: actionData?.taskId },
        });
        setJobSimulationPushNextEmail(true);
      };
      component = (
        <>
          <ActionButton
            action={action}
            title={objAction.title}
            label={objAction.label || 'Task details'}
          />
        </>
      );
      break;
    case 'viewFileDetail':
      action = () => {
        // setJobSimulationApp({
        //   appId: 'files',
        //   data: { fileUrl: actionData?.fileUrl },
        // });
        setJobSimulationEmailFile({ fileUrl: actionData?.fileUrl || '' });
        // setJobSimulationPushNextEmail(true);
      };
      component = (
        <>
          <ActionButton
            action={action}
            title={objAction.title}
            label={objAction.label || 'Your file'}
          />
        </>
      );
      break;
    default:
      break;
  }

  return component;
};

interface EmailActionProps {
  email: EmailTemplateProps;
  actionDataIndex: number;
}

const EmailAction: React.FC<EmailActionProps> = ({ email, actionDataIndex }) => {
  const component = useEmailActions(email, actionDataIndex);
  return component;
};

const getEmailGreeting = (greeting: string, userName: string) => {
  let greetingText = greeting.replace('{user_name}', userName || '');
  if (!greetingText) greetingText = 'Hi';
  return greetingText;
};

interface EmailTemplateProps {
  title: string;
  senderName: string;
  senderEmail: string;
  senderAvatar: string;
  senderRole: string;
  time: number;
  data?: {
    logo?: string;
    greeting?: string;
    content: string;
    actions?: {
      type: 'viewTasks' | 'joinMeeting' | 'viewNewsDetails' | 'viewTaskDetails' | 'viewFileDetail';
      label: string;
      title?: string;

      data?: {
        // type = joinMeeting
        datetime?: number | string;
        duration?: string;
        meetingLink?: string;
        // type = viewNewsDetails
        newsId?: string;
        // type = viewTaskDetails
        taskId?: string;
        // type = viewFileDetail
        fileUrl?: string;

        // TODO: remove this after testing
        triggerAssistant?: string;
        pushNextEmail?: boolean;
      };
    }[];
    signature: {
      title?: string;
      company?: string;
    };
  };
  children?: React.ReactNode;
}

const EmailTemplate = (props: EmailTemplateProps) => {
  const { title, senderName, senderEmail, senderAvatar, senderRole, time, children, data } = props;

  // const user = useRecoilValue(store.user);
  const jobsimulationUser = useRecoilValue(store.jobSimulationUser);
  const [jobSimulationEmailFile, setJobSimulationEmailFile] = useRecoilState(
    store.jobSimulationEmailFile,
  );

  const buildFileUrl = (orUrl: string) => {
    const newUrl = new URL(orUrl);
    newUrl.searchParams.set('secret', jobsimulationUser?.email || '');

    return newUrl.toString();
  };

  return (
    <div className={cn('relative', jobSimulationEmailFile?.fileUrl ? 'h-full' : '')}>
      <div className="rounded-xl border p-5">
        <div className="flex items-center justify-between">
          <p className="text-2xl font-bold">{title}</p>
        </div>
        <hr className="my-5" />
        <div className="space-y-5">
          <div className="flex items-start gap-4">
            <img
              src={senderAvatar || '/assets/avatar.png'}
              alt="Avatar"
              className="h-12 w-12 rounded-full object-cover"
            />
            <div className="flex-1">
              <p className="font-semibol">
                {senderName}
                {senderRole ? ` - ${senderRole}` : ''}
              </p>
              <div className="flex items-center text-xs text-neutral-500">
                <span className="mr-1">{senderEmail}</span>
                <span className="mx-2 h-1 w-1 rounded-full bg-[#000] opacity-50"></span>
                <span className="text-xs opacity-50">
                  <EmailClock datetime={time} />
                </span>
              </div>
            </div>
          </div>

          {/* Email Content */}
          {children ? (
            children
          ) : !!data ? (
            <>
              {/* <img src={data.logo || '/assets/greentek-logo.png'} alt="Logo" className='max-w-[200px]' /> */}
              <div className="space-y-4 text-sm">
                <p className="font-semibold">
                  {getEmailGreeting(data.greeting || '', jobsimulationUser?.name || '')},
                </p>
                <div className="w-full max-w-3xl">
                  {/* TODO: Need replace {user_name} with actual user name, ... */}
                  <MarkdownEmail content={data.content} />
                </div>
              </div>

              <div className="flex flex-col">
                {data?.actions?.length && (
                  <div>
                    <div className="flex flex-col gap-4">
                      {data?.actions?.map((_action, index) => (
                        <div key={index}>
                          <EmailAction email={props} actionDataIndex={index} key={index} />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <p className="mt-8 flex flex-col gap-2 text-sm">
                  <span>{data.signature.title || 'Warm regards'},</span>
                  <span className="font-semibold">
                    {senderName}
                    {senderRole ? ` - ${senderRole}` : ''}
                  </span>
                  {data.signature.company && (
                    <span className="font-semibold">{data.signature.company}</span>
                  )}
                </p>
              </div>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>

      {jobSimulationEmailFile && (
        <div className="absolute left-0 top-0 h-full w-full bg-white">
          <button
            onClick={() => setJobSimulationEmailFile(null)}
            className="absolute right-0 top-0 flex h-6 w-6 items-center justify-center rounded-full text-[#111111] transition-transform duration-200"
          >
            <XMarkIcon className="h-full w-full" />
          </button>
          <iframe
            width="100%"
            height="100%"
            // src={`${jobSimulationEmailFile.fileUrl}?secret=${jobsimulationUser?.email}`}
            src={buildFileUrl(jobSimulationEmailFile.fileUrl)}
          ></iframe>
        </div>
      )}
    </div>
  );
};

export default EmailTemplate;
