import type { UseMutationResult } from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type * as t from 'librechat-data-provider';
import { MutationKeys, dataService } from 'librechat-data-provider';

export const useSaveUserInteraction = (
  options?: t.MutationOptions<
    any,
    { jobSimulationId: string; jobSimulationEmail: string; interaction: any },
    unknown,
    unknown
  >,
): UseMutationResult<
  any,
  unknown,
  { jobSimulationId: string; jobSimulationEmail: string; interaction: any },
  unknown
> => {
  return useMutation([MutationKeys.saveJobSimulationUserInteraction], {
    mutationFn: (payload: {
      jobSimulationId: string;
      jobSimulationEmail: string;
      interaction: any;
    }) => dataService.saveJobSimulationUserInteraction(payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useDeleteUserInteraction = (
  options?: t.MutationOptions<
    any,
    { jobSimulationId: string; jobSimulationEmail: string },
    unknown,
    unknown
  >,
): UseMutationResult<
  any,
  unknown,
  { jobSimulationId: string; jobSimulationEmail: string },
  unknown
> => {
  return useMutation([MutationKeys.saveJobSimulationUserInteraction], {
    mutationFn: (payload: { jobSimulationId: string; jobSimulationEmail: string }) =>
      dataService.deleteJobSimulationUserInteraction(payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useUpdateJobSimulationLogoMutation = (
  options?: t.MutationOptions<t.TJobSimulationDataResponse, FormData, unknown, unknown>,
): UseMutationResult<t.TJobSimulationDataResponse, unknown, FormData, unknown> => {
  return useMutation({
    mutationFn: (formData: FormData) => dataService.updateJobSimulationLogo(formData),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useUpdateJobSimulationProgress = (
  options?: t.MutationOptions<
    t.TJobSimulationProgress,
    { jobSimulationId: string; [key: string]: any },
    unknown,
    unknown
  >,
): UseMutationResult<
  t.TJobSimulationProgress,
  unknown,
  { jobSimulationId: string; [key: string]: any },
  unknown
> => {
  return useMutation({
    mutationFn: (data: { jobSimulationId: string; [key: string]: any }) => {
      const { jobSimulationId, ...rest } = data;
      return dataService.updateJobSimulationProgress({ jobSimulationId, data: rest });
    },
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};
