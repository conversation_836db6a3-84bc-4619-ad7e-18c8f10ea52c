import { motion } from 'framer-motion';
import type * as t from 'librechat-data-provider';
import { TJobSimulationEmail } from 'librechat-data-provider/dist/types';
import { useOutletContext } from 'react-router-dom';
import { useRecoilState } from 'recoil';
import { TJobSimulationContext } from '~/common';
import { TooltipAnchor } from '~/components/ui';
import { useUpdateJobSimulationProgress } from '~/data-provider/JobSimulation/mutations';
import store from '~/store';
import EmailClock from './EmailClock';

const ListEmails = () => {
  const { jobSimulationData } = useOutletContext<TJobSimulationContext>();
  const [jobSimulationEmails, setJobSimulationEmails] = useRecoilState(store.jobSimulationEmails);
  const [jobSimulationCurrentEmail, setJobSimulationCurrentEmail] = useRecoilState(
    store.jobSimulationCurrentEmail,
  );

  const [jobSimulationMeetings, setJobSimulationMeetings] = useRecoilState(
    store.jobSimulationMeetings,
  );

  const updateJobSimulationProgress = useUpdateJobSimulationProgress();

  const handleClick = (selectedEmail: t.TJobSimulationEmail) => {
    const progressEmails: { id: string; read: boolean; time: number }[] = [];
    const updatedEmails = jobSimulationEmails.map((email: TJobSimulationEmail) => {
      progressEmails.push({
        id: email.id,
        read: email.id === selectedEmail.id ? true : !!email.read,
        time: email.time!,
      });

      if (email.id === selectedEmail.id) {
        return { ...email, read: true };
      }
      return email;
    });

    updateJobSimulationProgress.mutateAsync({
      jobSimulationId: jobSimulationData?.jobSimulationId!,
      emails: progressEmails,
    });

    const emailAction = selectedEmail.data?.actions?.find(
      (action) => action.type === 'joinMeeting',
    );
    if (
      emailAction &&
      !jobSimulationMeetings.find((meeting) => meeting.id === `${selectedEmail.id}`)
    ) {
      let meetingDateTime = emailAction.data?.datetime;
      if (
        meetingDateTime &&
        typeof meetingDateTime === 'string' &&
        meetingDateTime.includes('email_time')
      ) {
        meetingDateTime = selectedEmail.time;
      }
      setJobSimulationMeetings((prevMeetings) => [
        ...prevMeetings,
        {
          id: `${selectedEmail.id}`,
          title: emailAction.title!,
          datetime: Number(meetingDateTime),
          link: emailAction.data?.meetingLink!,
          status: 'ongoing',
        },
      ]);
    }

    setJobSimulationCurrentEmail(selectedEmail);
    setTimeout(() => {
      setJobSimulationEmails(updatedEmails);
    }, 200);
  };

  return (
    <>
      {jobSimulationEmails.map((item: t.TJobSimulationEmail, index: number) => (
        <div key={item.id} className="space-y-2">
          {index === 0 && <hr className="border-neutral-200" />}
          <motion.div
            onClick={() => handleClick(item)}
            initial={false}
            animate={{
              scale: jobSimulationCurrentEmail?.id === item.id ? 1.04 : 1,
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 20, mass: 0.5 }}
            className={`cursor-pointer rounded-lg p-4 ${
              jobSimulationCurrentEmail?.id === item.id ? 'bg-[#F0F0F0]' : 'hover:bg-neutral-100'
            }`}
          >
            <div className="flex items-start justify-between">
              <TooltipAnchor
                description={item.title}
                side="right"
                toolTipClassName="max-w-[400px]"
                render={
                  <div className="line-clamp-1 text-sm font-semibold leading-5 text-neutral-900">
                    {item.title}
                  </div>
                }
              />
              {/* <div className="line-clamp-1 text-sm font-semibold leading-5 text-neutral-900">
                {item.title}
              </div> */}
              {!item.read && <div className="mt-1 h-2 w-2 shrink-0 rounded-full bg-red-500"></div>}
            </div>
            <div className="flex items-center justify-between">
              <p className="text-[10px]">
                From: {/* TODO: truncate, ellipsis */}
                <span className="rounded-xl bg-[#F0F0F0] px-[6px] py-[2px] font-medium">
                  {item.name} - {item.role}
                </span>
              </p>
              <p className="text-xs opacity-50">
                <EmailClock datetime={item.time!} />
              </p>
            </div>
            <TooltipAnchor
              description={item.desc}
              side="right"
              toolTipClassName="max-w-[400px]"
              render={
                <div className="line-clamp-2 text-xs leading-4 text-neutral-500">{item.desc}</div>
              }
            />
          </motion.div>
          <hr className="border-neutral-200" />
        </div>
      ))}
    </>
  );
};

export default ListEmails;
