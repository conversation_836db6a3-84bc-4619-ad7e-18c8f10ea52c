import React, { memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { useRecoilValue } from 'recoil';
import rehypeHighlight from 'rehype-highlight';
import rehypeKatex from 'rehype-katex';
import remarkDirective from 'remark-directive';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import supersub from 'remark-supersub';
import type { Pluggable } from 'unified';
import CustomButton from '~/components/CustomButtons/CustomButton';
import { customButtonPlugin } from '~/components/CustomButtons/CustomButtonPlugin';
import store from '~/store';
import { langSubset, preprocessLaTeX } from '~/utils';

type TParagraphProps = {
  children: React.ReactNode;
};

export const p: React.ElementType = memo(({ children }: TParagraphProps) => {
  return <p className="mb-2 whitespace-pre-wrap">{children}</p>;
});

type TContentProps = {
  content: string;
};

const MarkdownEmail = memo(({ content = '' }: TContentProps) => {
  const LaTeXParsing = useRecoilValue<boolean>(store.LaTeXParsing);

  const currentContent = useMemo(() => {
    return LaTeXParsing ? preprocessLaTeX(content) : content;
  }, [content, LaTeXParsing]);

  const rehypePlugins = useMemo(
    () => [
      [rehypeKatex, { output: 'mathml' }],
      [
        rehypeHighlight,
        {
          detect: true,
          ignoreMissing: true,
          subset: langSubset,
        },
      ],
    ],
    [],
  );

  const remarkPlugins: Pluggable[] = useMemo(
    () => [
      supersub,
      remarkGfm,
      remarkDirective,
      customButtonPlugin,
      [remarkMath, { singleDollarTextMath: true }],
    ],
    [],
  );

  return (
    <ReactMarkdown
      /** @ts-ignore */
      remarkPlugins={remarkPlugins}
      /* @ts-ignore */
      rehypePlugins={rehypePlugins}
      components={
        {
          p,
          callToAction: CustomButton,
        } as {
          [nodeType: string]: React.ElementType;
        }
      }
    >
      {currentContent}
    </ReactMarkdown>
  );
});

export default MarkdownEmail;
