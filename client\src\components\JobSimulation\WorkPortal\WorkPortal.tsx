import { format } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { TJobSimulationContext } from '~/common';
import { useDeleteUserInteraction } from '~/data-provider/JobSimulation/mutations';
import useOpenCloseApp from '~/hooks/useOpenCloseApp';
import store from '~/store';
import { CalendarIcon, ClockIcon, PowerIcon } from '../../svg';
import { DockAppItem } from '../../ui/DockAppItem';
import { WindowFrame } from '../../ui/WindowFrame';
import Mails from '../Email/Mails';
import MailsControlCenter from '../Email/MailsControlCenter';
import JobSimulationLoginForm from '../JobSimulationLoginForm';
import JobSimulationLogo from '../JobSimulationLogo';
import JobSimulationUser from '../JobSimulationUser';
import Meeting from '../Meeting';
import MongoDBCompass from '../MongoDBCompass';
import News from '../News';
import TaskBoard from '../TaskBoard';
import FacebookAd from '../FacebookAd';
import FacebookAdSimple from '../FacebookAdSimple';
import FacebookAds from '../FacebookAds';

const apps = [
  { id: 'mail', name: 'Mail', image: '/assets/job-simulation/mail.png', component: Mails },
  // { id: 'dashboard', name: 'Dashboard', image: '/assets/job-simulation/dashboard.png', component: Dashboard },
  // { id: 'community', name: 'Community', image: '/assets/job-simulation/team.png', component: Team },
  {
    id: 'task-board',
    name: 'Task Board',
    image: '/assets/job-simulation/taskboard.png',
    component: TaskBoard,
  },
  // {
  //   id: 'calendar',
  //   name: 'Calendar',
  //   image: '/assets/job-simulation/calendar.png',
  //   component: Calendar,
  // },
  {
    id: 'meeting',
    name: 'Meeting',
    image: '/assets/job-simulation/meeting.png',
    component: Meeting,
  },
  { id: 'news', name: 'News', image: '/assets/job-simulation/pulse.png', component: News },
];

function AppLoadingOverlay() {
  return (
    <div className="flex h-full flex-col items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="relative w-full max-w-[600px]"
      >
        <div className="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 flex-col items-center justify-center">
          <div className="relative mb-5">
            <motion.div
              className="absolute inset-0 z-0 rounded-xl bg-white/60 blur-2xl"
              animate={{ opacity: [0.5, 0.9, 0.5], scale: [1, 1.05, 1] }}
              transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
            />
            <motion.div
              className="relative z-10 max-w-[173px]"
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
              <img src="/assets/job-simulation/loading-data.png" className="h-full w-full" />
            </motion.div>
          </div>
          <motion.p
            className="text-xl font-semibold text-gray-800"
            animate={{ opacity: [0.6, 1, 0.6] }}
            transition={{ duration: 1.6, repeat: Infinity }}
          >
            Data is being uploaded!
          </motion.p>
          <motion.p
            className="mt-1 text-xl font-medium text-gray-700"
            animate={{ opacity: [1, 0.4, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Please wait a moment...
          </motion.p>
        </div>
      </motion.div>
    </div>
  );
}

// const useIsMd = () => {
//   const [isMd, setIsMd] = useState(false);

//   useEffect(() => {
//     const handleResize = () => setIsMd(window.innerWidth >= 768);
//     handleResize();
//     window.addEventListener('resize', handleResize);
//     return () => window.removeEventListener('resize', handleResize);
//   }, []);

//   return isMd;
// };

export default function WorkPortal() {
  const formattedDate = format(new Date(), 'EEEE, d MMMM');
  const formattedTime = format(new Date(), 'HH:mm');

  const { jobSimulationData } = useOutletContext<TJobSimulationContext>();

  const { jobSimulationId = '' } = useParams();
  // const [isTaskBoardVisible, setIsTaskBoardVisible] = useState(true);
  const { openApp, closeApp } = useOpenCloseApp();

  const [jobSimulationApp, setJobSimulationApp] = useRecoilState(store.jobSimulationApp);
  const jobSimulationUser = useRecoilValue(store.jobSimulationUser);

  const setJobSimulationEmailFile = useSetRecoilState(store.jobSimulationEmailFile);
  const setJobsimulationIsOpenWorkPortal = useSetRecoilState(store.jobSimulationIsOpenWorkPortal);
  const setJobSimulationMeetings = useSetRecoilState(store.jobSimulationMeetings);
  const setJobsimulationUser = useSetRecoilState(store.jobSimulationUser);
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const deleteUserInteraction = useDeleteUserInteraction();

  const isAppLoading = (() => {
    switch (jobSimulationApp?.appId) {
      case 'mail':
        return !!jobSimulationData;
      default:
        return false;
    }
  })();

  const selectedApp = apps.find((app) => app.id === jobSimulationApp?.appId);
  const AppComponent = selectedApp?.component;

  const handleLogout = () => {
    setJobsimulationIsOpenWorkPortal(false);
    setJobSimulationApp({ appId: '' });
    setJobSimulationEmailFile(null);
    setJobsimulationUser(null);
    setJobSimulationMeetings([]);
    setJobsimulationTriggerMessage(null);

    deleteUserInteraction.mutate({
      jobSimulationId: jobSimulationId!,
      jobSimulationEmail: jobSimulationUser?.email || '',
    });
  };

  // Logo selection using switch for scalability
  let logoSrc = jobSimulationData?.logo || '';
  switch (jobSimulationId) {
    case 'digital-marketing':
      logoSrc = logoSrc || '/assets/advergo-logo.png';
      break;
    case 'esg-analyst':
      logoSrc = logoSrc || '/assets/greentek-logo.png';
      break;
    default:
      logoSrc = logoSrc || '/assets/greentek-logo.png';
  }

  // let backgroundSrc;
  // switch (jobSimulationId) {
  //   case 'digital-marketing':
  //     backgroundSrc = '/assets/job-simulation/background-hive.png';
  //     break;
  //   case 'esg-analyst':
  //     backgroundSrc = '/assets/job-simulation/background-greentek-hive.png';
  //     break;
  //   default:
  //     backgroundSrc = '/assets/job-simulation/background-hive.png';
  // }

  // useEffect(() => {
  //   const handleHashChange = () => {
  //     const hash = window.location.hash.replace('#', '');
  //     const targetApp = apps.find((app) => app.name.toLowerCase().replace(/\s+/g, '-') === hash);
  //     if (targetApp) {
  //       setJobSimulationApp({
  //         appId: targetApp.id,
  //       });
  //     }
  //   };

  //   handleHashChange();
  // }, []);

  useEffect(() => {
    if (!jobSimulationApp?.appId) return;
    window.location.hash = `#${jobSimulationApp.appId}`;
  }, [jobSimulationApp]);

  if (!jobSimulationUser) {
    return <JobSimulationLoginForm />;
  }

  return (
    <>
      <div className="relative flex h-screen flex-col overflow-hidden text-[#3F3F3F]">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="relative z-30"
        >
          <div
            className="absolute h-full w-full bg-white opacity-80"
            style={{ boxShadow: '0px 0px 6px 0px rgba(0, 0, 0, 0.10)' }}
          />
          <div className="relative z-20 flex items-center justify-between px-6 py-2.5">
            <div className="flex items-center space-x-8">
              <img src={logoSrc} className="h-10 rounded-md" />
              <div className="flex items-center gap-2">
                <JobSimulationUser />
              </div>
            </div>
            {/* <div className="w-full max-w-md">
              <div className="flex h-12 w-full items-center rounded-full border border-gray-300 bg-white px-4 shadow-sm">
                <SearchIcon />
                <input
                  type="text"
                  placeholder="Search"
                  className="ml-3 flex-1 bg-transparent text-gray-600 placeholder-gray-400 outline-none focus:outline-none focus:ring-0"
                />
              </div>
            </div> */}
            <div className="flex items-center gap-5">
              {/* <p className="relative cursor-pointer">
                <BellIcon />
                <span className="absolute -right-1 -top-1 h-2.5 w-2.5 rounded-full border-2 border-white bg-[#FF5858]" />
              </p>
              <VerticalLineIcon />
              <p className="cursor-pointer">
                <img src="/assets/job-simulation/messages.png" className="h-10 w-10" />
              </p> */}
              {/* <VerticalLineIcon /> */}
              <div className="flex items-center gap-4">
                <div className="flex cursor-pointer items-center gap-1 text-sm">
                  <span>
                    <CalendarIcon />
                  </span>
                  <span>{formattedDate}</span>
                </div>
                <div className="flex cursor-pointer items-center gap-1 text-sm">
                  <span>
                    <ClockIcon />
                  </span>
                  <span>{formattedTime}</span>
                </div>
                <div className="cursor-pointer" onClick={handleLogout}>
                  <PowerIcon size={16} />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut', delay: 0.1 }}
          className="relative min-h-0 flex-1 px-6 pb-4 pt-6 xl:px-6 xl:pb-4 xl:pt-6"
        >
          {/* Content */}
          {/* {jobSimulationData?.logo ? (
            <div className="absolute left-1/2 top-1/2 w-full max-w-md -translate-x-1/2 -translate-y-1/2 flex justify-center">
              <img src={backgroundSrc} alt="Background" className='max-w-[200px]' />
            </div>
          ) : (
            <div className="absolute left-1/2 top-1/2 w-full max-w-md -translate-x-1/2 -translate-y-1/2">
              <img src={backgroundSrc} alt="Background" />
            </div>
          )} */}
          <div className="absolute left-1/2 top-1/2 flex w-full max-w-md -translate-x-1/2 -translate-y-1/2 justify-center">
            {/* <img src={backgroundSrc} alt="Background" /> */}
            <JobSimulationLogo logo={jobSimulationData?.logo || ''} />
          </div>
          {/* App */}
          <div className="relative h-full min-h-0 w-full">
            {isAppLoading && <AppLoadingOverlay />}

            <AnimatePresence>
              <motion.div
                key="task-board"
                initial={false}
                animate={
                  // jobSimulationApp?.appId === 'task-board' && isTaskBoardVisible
                  jobSimulationApp?.appId === 'task-board'
                    ? { opacity: 1, scale: 1, pointerEvents: 'auto' }
                    : { opacity: 0, scale: 0.95, pointerEvents: 'none' }
                }
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="absolute bottom-0 left-0 top-0 flex w-full justify-center"
              >
                <WindowFrame
                  title="Task Board"
                  onClose={() => {
                    closeApp();
                    // setIsTaskBoardVisible(false);
                  }}
                  className="h-full w-full overflow-hidden"
                >
                  <TaskBoard />
                </WindowFrame>
              </motion.div>

              {/* TODO: MongoDB Compass. Just for testing purposes. Not ready for production yet.  */}
              {/* {jobSimulationApp?.appId && jobSimulationApp?.appId === 'mongodb-compass' && (
                <motion.div
                  key={jobSimulationApp.appId}
                  initial={{ opacity: 0, scale: 0.9, originY: 1 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="absolute bottom-0 left-0 top-0 flex w-full justify-center"
                >
                  <WindowFrame
                    title="MongoDB Compass"
                    onClose={() => closeApp()}
                    className="h-full w-full overflow-hidden"
                    appContainerClassName="overflow-y-auto"
                  >
                    <MongoDBCompass />
                  </WindowFrame>
                </motion.div>
              )} */}

              {jobSimulationData?.jobSimulationId === 'digital-marketing' && (
                <motion.div
                  key="facebook-ad"
                  initial={false}
                  animate={
                    // jobSimulationApp?.appId === 'task-board' && isTaskBoardVisible
                    jobSimulationApp?.appId === 'facebook-ad'
                      ? { opacity: 1, scale: 1, pointerEvents: 'auto' }
                      : { opacity: 0, scale: 0.95, pointerEvents: 'none' }
                  }
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="absolute bottom-0 left-0 top-0 flex w-full justify-center"
                >
                  <WindowFrame
                    title="Facebook Ad"
                    onClose={() => closeApp()}
                    className="h-full w-full overflow-hidden"
                    appContainerClassName="overflow-y-auto"
                    showBrowserSearchbar
                    browserSearchbarUrl="https://business.facebook.com/latest/ads_creation"
                  >
                    {/* <FacebookAd /> */}
                    <FacebookAdSimple />
                  </WindowFrame>
                </motion.div>
              )}

              {/* {jobSimulationData?.jobSimulationId === 'digital-marketing' && (
                <motion.div
                  key="fb-ad"
                  initial={false}
                  animate={
                    jobSimulationApp?.appId === 'fb-ad'
                      ? { opacity: 1, scale: 1, pointerEvents: 'auto' }
                      : { opacity: 0, scale: 0.95, pointerEvents: 'none' }
                  }
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="absolute bottom-0 left-0 top-0 flex w-full justify-center"
                >
                  <WindowFrame
                    title="Facebook Ad"
                    onClose={() => closeApp()}
                    className="h-full w-full overflow-hidden"
                    appContainerClassName="overflow-y-auto"
                  >
                    <FacebookAds />
                  </WindowFrame>
                </motion.div>
              )} */}

              {jobSimulationApp?.appId &&
                jobSimulationApp?.appId !== 'task-board' &&
                AppComponent && (
                  <motion.div
                    key={jobSimulationApp.appId}
                    initial={{ opacity: 0, scale: 0.9, originY: 1 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                    className="absolute bottom-0 left-0 top-0 flex w-full justify-center"
                  >
                    <WindowFrame
                      title={selectedApp?.name}
                      className="h-full w-full overflow-hidden"
                      showMailActions={jobSimulationApp?.appId === 'mail'}
                      onClose={() => closeApp()}
                    >
                      <AppComponent />
                    </WindowFrame>
                  </motion.div>
                )}
            </AnimatePresence>
          </div>
        </motion.div>
        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 0.2 }}
          className="relative"
        >
          <div className="xxl:h-[70px] flex h-[56px] w-full items-baseline">
            <img
              src="/assets/dock/rectangle.png"
              className="xxl:h-[43px] h-[34px] w-[20%] object-fill"
            />
            <img src="/assets/dock/left-curve.png" className="h-full w-[15%] object-fill" />
            <img src="/assets/dock/rect-center.png" className="h-full w-[30%] object-fill" />
            <img src="/assets/dock/right-curve.png" className="h-full w-[15%] object-fill" />
            <img
              src="/assets/dock/rectangle.png"
              className="xxl:h-[43px] h-[34px] w-[20%] object-fill"
            />
          </div>
          <div className="absolute left-1/2 top-[40%] z-20 flex -translate-x-1/2 -translate-y-1/2 items-center justify-between p-1 md:p-3">
            <>
              {apps.map(({ id, name, image }) => {
                return (
                  <DockAppItem
                    key={id}
                    appId={id}
                    name={name}
                    image={image}
                    isActive={jobSimulationApp?.appId === id}
                    onClick={() => {
                      // if (id === 'task-board') setIsTaskBoardVisible(true);
                      openApp(id);
                      // setJobSimulationApp({
                      //   appId: id,
                      // });
                    }}
                  />
                );
              })}
              {/* <DockAppItem
                key={'mongodb-compass'}
                appId={'mongodb-compass'}
                name={'MongoDB Compass'}
                image={'/assets/job-simulation/mongodb-compass.png'}
                isActive={jobSimulationApp?.appId === 'mongodb-compass' && isTaskBoardVisible}
                onClick={() => {
                  openApp('mongodb-compass');
                }}
              /> */}
              {jobSimulationData?.jobSimulationId === 'digital-marketing' && (
                <DockAppItem
                  key={'facebook-ad'}
                  appId={'facebook-ad'}
                  name={'Facebook Ad'}
                  image={'/assets/job-simulation/ad.png'}
                  isActive={jobSimulationApp?.appId === 'facebook-ad'}
                  onClick={() => {
                    openApp('facebook-ad');
                  }}
                />
              )}
              {/* {jobSimulationData?.jobSimulationId === 'digital-marketing' && (
                <DockAppItem
                  key={'fb-ad'}
                  appId={'fb-ad'}
                  name={'Facebook Ad'}
                  image={'/assets/job-simulation/ad.png'}
                  isActive={jobSimulationApp?.appId === 'fb-ad'}
                  onClick={() => {
                    openApp('fb-ad');
                  }}
                />
              )} */}
            </>
          </div>
        </motion.div>
      </div>
      <MailsControlCenter />
    </>
  );
}
